# Portfolio Website - Docker Setup

This repository contains the portfolio website for Muhammad <PERSON>, containerized using Docker for easy deployment and development.

## Quick Start

### Prerequisites
- Docker installed on your system
- Docker Compose installed on your system

### Running the Application

1. **Clone or navigate to the project directory:**
   ```bash
   cd /path/to/portfolio
   ```

2. **Start the application:**
   ```bash
   docker-compose up
   ```

3. **Access the website:**
   Open your browser and navigate to: `http://localhost:8068`

4. **Stop the application:**
   ```bash
   docker-compose down
   ```

### Development Mode

For development with automatic rebuilding:
```bash
docker-compose up --build
```

To run in detached mode (background):
```bash
docker-compose up -d
```

## Project Structure

```
portfolio/
├── index.html          # Main HTML file
├── styles.css          # CSS styles
├── script.js           # JavaScript functionality
├── faizan.jpg          # Profile image
├── docker-compose.yml  # Docker Compose configuration
├── Dockerfile          # Docker image definition
├── nginx.conf          # Nginx server configuration
├── .dockerignore       # Docker ignore file
└── README.md           # This file
```

## Docker Configuration

### Services
- **portfolio**: Nginx web server serving the static portfolio files

### Features
- **Port Mapping**: Application accessible on port 8068
- **Health Checks**: Automatic health monitoring
- **Security**: Non-root user execution
- **Optimization**: Gzip compression and caching headers
- **Auto-restart**: Container restarts unless manually stopped

### Nginx Configuration
- Serves static files efficiently
- Implements security headers
- Enables gzip compression
- Sets appropriate cache headers
- Includes health check endpoint at `/health`

## Customization

### Changing the Port
To change the port from 8068 to another port, edit the `docker-compose.yml` file:
```yaml
ports:
  - "YOUR_PORT:80"
```

### Modifying Nginx Configuration
Edit the `nginx.conf` file to customize server behavior, then rebuild:
```bash
docker-compose up --build
```

## Troubleshooting

### Port Already in Use
If port 8068 is already in use, either:
1. Stop the service using that port, or
2. Change the port in `docker-compose.yml`

### Container Won't Start
Check the logs:
```bash
docker-compose logs portfolio
```

### Rebuilding After Changes
After making changes to the website files:
```bash
docker-compose up --build
```

## Production Deployment

For production deployment, consider:
1. Using a reverse proxy (like Traefik or nginx-proxy)
2. Adding SSL/TLS certificates
3. Setting up proper logging
4. Implementing monitoring and alerting

## Health Check

The application includes a health check endpoint at `/health` that returns a simple "healthy" response when the service is running properly.
