/* ===== CSS CUSTOM PROPERTIES (VARIABLES) ===== */
:root {
  /* Colors */
  --primary-color: #00d4aa;
  --primary-dark: #00b894;
  --secondary-color: #6c5ce7;
  --accent-color: #fd79a8;
  --text-primary: #e2e8f0;
  --text-secondary: #cbd5e0;
  --text-light: #a0aec0;
  --background-primary: #1a202c;
  --background-secondary: #2d3748;
  --background-dark: #171923;
  --background-card: #2d3748;
  --border-color: #4a5568;
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.4);
  --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.5);

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;

  /* Layout */
  --container-max-width: 1200px;
  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}





/* ===== RESET AND BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--background-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== DARK THEME ENHANCEMENTS ===== */
/* Scrollbar styling (webkit browsers) */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Selection styling */
::selection {
  background: var(--primary-color);
  color: var(--background-dark);
}

::-moz-selection {
  background: var(--primary-color);
  color: var(--background-dark);
}

/* Focus styles */
*:focus {
  outline-color: var(--primary-color);
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover,
a:focus {
  color: var(--primary-dark);
  outline: none;
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-4xl);
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius-sm);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: 0;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--background-dark);
  font-weight: var(--font-weight-semibold);
  box-shadow: var(--shadow-light);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  color: var(--background-dark);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: var(--background-dark);
  transform: translateY(-2px);
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(26, 32, 44, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: var(--z-fixed);
  transition: all var(--transition-normal);
}

.nav-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-logo a {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  text-decoration: none;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
}

.nav-menu a {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  position: relative;
}

.nav-menu a:hover,
.nav-menu a:focus {
  color: var(--primary-color);
  background: rgba(0, 212, 170, 0.1);
}

.nav-menu a.active {
  color: var(--primary-color);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}



.nav-toggle span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: all var(--transition-normal);
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 212, 170, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 212, 170, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(74, 85, 104, 0.1) 0%, transparent 50%);
  opacity: 0.6;
  z-index: 1;
}

.hero-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 2;
}

.hero-content {
  display: grid;
  grid-template-columns: 1.2fr 0.8fr;
  gap: var(--spacing-4xl);
  align-items: center;
  min-height: 80vh;
  padding: var(--spacing-2xl) 0;
}

.hero-text {
  animation: fadeInUp 1s ease-out;
}

.hero-title {
  margin-bottom: var(--spacing-xl);
}

.hero-greeting {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.hero-name {
  display: block;
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-color), rgba(0, 212, 170, 0.8), var(--text-primary));
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  animation:
    gradient-shift 4s ease-in-out 3s infinite,
    glow 3s ease-in-out 3s infinite,
    bounceIn 0.8s ease-out 0.5s both;
}

.hero-name.typing::after {
  content: '|';
  color: var(--primary-color);
  animation: blink 1s infinite;
  margin-left: 2px;
}

.hero-role {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.hero-description {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-2xl);
  max-width: 520px;
  color: var(--text-secondary);
  opacity: 0.9;
}

.hero-cta {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInRight 1s ease-out 0.3s both;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
}

.hero-image img {
  width: 100%;
  max-width: 280px;
  height: auto;
  border-radius: 50%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 0 4px rgba(0, 212, 170, 0.2);
  border: 4px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-slow);
  position: relative;
}

.hero-image {
  position: relative;
}

.hero-image::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--primary-color));
  z-index: -1;
  opacity: 0.15;
  animation: rotate 20s linear infinite;
}

.hero-image img:hover {
  transform: scale(1.03);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 0 0 6px rgba(0, 212, 170, 0.3);
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(-50px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(0);
  }
  70% {
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

@keyframes glow {
  0%, 100% {
    text-shadow: 0 0 3px rgba(0, 212, 170, 0.2);
  }
  50% {
    text-shadow: 0 0 8px rgba(0, 212, 170, 0.3), 0 0 12px rgba(0, 212, 170, 0.2);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.hero-scroll {
  position: absolute;
  bottom: var(--spacing-2xl);
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.hero-scroll a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  color: var(--primary-color);
  font-size: var(--font-size-lg);
  transition: all var(--transition-normal);
}

.hero-scroll a:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: var(--spacing-4xl) 0;
  background: var(--background-secondary);
}

.about-content {
  max-width: 800px;
  margin: 0 auto;
}

.about-text h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-xl);
}

.about-text p {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-2xl);
}

.about-highlights {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
  margin-top: var(--spacing-4xl);
}

@media (max-width: 1024px) {
  .about-highlights {
    grid-template-columns: repeat(2, 1fr);
  }
}

.highlight-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--background-card);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-light);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  min-height: 180px;
}

.highlight-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.highlight-item:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-heavy);
  border-color: var(--primary-color);
}

.highlight-item i {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(0, 212, 170, 0.1);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.highlight-item div {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.highlight-item h4 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1;
}

.highlight-item p {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin-bottom: 0;
  font-weight: var(--font-weight-medium);
}

/* ===== SKILLS SECTION ===== */
.skills {
  padding: var(--spacing-4xl) 0;
  background: var(--background-primary);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
}

.skill-category {
  background: var(--background-card);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-light);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.skill-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  transform: translateX(-100%);
  transition: transform var(--transition-normal);
}

.skill-category:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-heavy);
  border-color: var(--primary-color);
}

.skill-category:hover::before {
  transform: translateX(0);
}

.skill-category h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.skill-category i {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
}

.skill-list {
  list-style: none;
}

.skill-list li {
  padding: var(--spacing-sm) 0;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
  transition: color var(--transition-fast);
}

.skill-list li:last-child {
  border-bottom: none;
}

.skill-list li:hover {
  color: var(--primary-color);
}

/* ===== EXPERIENCE SECTION ===== */
.experience {
  padding: var(--spacing-4xl) 0;
  background: var(--background-secondary);
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--primary-color);
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--spacing-4xl);
  width: 50%;
}

.timeline-item:nth-child(odd) {
  left: 0;
  padding-right: var(--spacing-3xl);
}

.timeline-item:nth-child(even) {
  left: 50%;
  padding-left: var(--spacing-3xl);
}

.timeline-content {
  background: var(--background-card);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-light);
  position: relative;
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
}

.timeline-item:hover .timeline-content {
  transform: translateY(-5px);
  box-shadow: var(--shadow-heavy);
  border-color: var(--primary-color);
}

.timeline-item:nth-child(odd) .timeline-content::after {
  content: '';
  position: absolute;
  right: -10px;
  top: 20px;
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-left-color: var(--background-card);
}

.timeline-item:nth-child(even) .timeline-content::after {
  content: '';
  position: absolute;
  left: -10px;
  top: 20px;
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-right-color: var(--background-card);
}

.timeline-date {
  display: inline-block;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  box-shadow: var(--shadow-light);
  margin-bottom: var(--spacing-md);
}

.timeline-dot {
  position: absolute;
  top: 25px;
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border: 4px solid white;
  border-radius: 50%;
  box-shadow: var(--shadow-medium);
  z-index: 3;
  transition: all var(--transition-normal);
}

.timeline-item:hover .timeline-dot {
  transform: scale(1.2);
  background: var(--secondary-color);
}

.timeline-item:nth-child(odd) .timeline-dot {
  right: -10px;
}

.timeline-item:nth-child(even) .timeline-dot {
  left: -10px;
}

.timeline-content h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.timeline-content h4 {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-md);
}

.timeline-content p {
  margin-bottom: var(--spacing-md);
}

.timeline-content ul {
  list-style: none;
  margin-left: var(--spacing-md);
}

.timeline-content li {
  position: relative;
  padding-left: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
}

.timeline-content li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* ===== EDUCATION SECTION ===== */
.education {
  padding: var(--spacing-4xl) 0;
  background: var(--background-primary);
}

.education-content {
  max-width: 1000px;
  margin: 0 auto;
}

.education-subsection {
  margin-bottom: var(--spacing-4xl);
}

.education-subsection:last-child {
  margin-bottom: 0;
}

.subsection-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
  text-align: center;
  justify-content: center;
}

.subsection-header i {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
  padding: var(--spacing-md);
  background: rgba(0, 212, 170, 0.1);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.subsection-header h3 {
  font-size: var(--font-size-2xl);
  color: var(--text-primary);
  margin-bottom: 0;
}

/* Formal Education Cards */
.education-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
}

.education-card {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
}

.education-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-color);
}

.education-icon {
  flex-shrink: 0;
}

.education-icon i {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
  padding: var(--spacing-md);
  background: rgba(0, 212, 170, 0.1);
  border-radius: var(--border-radius-md);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.education-info h4 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.education-info .institution {
  font-size: var(--font-size-base);
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-sm);
}

.education-info .description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: 0;
}

/* Learning Philosophy */
.learning-philosophy {
  background: var(--background-secondary);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-xl);
  border: 1px solid var(--border-color);
}

.philosophy-intro {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  font-style: italic;
  padding: var(--spacing-lg);
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  border-left: 4px solid var(--primary-color);
}

.learning-methods {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

@media (max-width: 1024px) {
  .learning-methods {
    grid-template-columns: 1fr;
  }
}

.learning-method {
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
}

.learning-method:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.method-icon {
  flex-shrink: 0;
}

.method-icon i {
  font-size: var(--font-size-xl);
  color: var(--secondary-color);
  padding: var(--spacing-md);
  background: rgba(108, 92, 231, 0.1);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.method-content h4 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.method-content ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.method-content li {
  position: relative;
  padding-left: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.method-content li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: var(--secondary-color);
  font-weight: bold;
}

.learning-philosophy-conclusion {
  background: var(--background-card);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
}

.learning-philosophy-conclusion h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.learning-philosophy-conclusion p {
  margin-bottom: var(--spacing-lg);
}

.proof-of-expertise {
  background: linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(74, 85, 104, 0.15));
  border: 1px solid rgba(0, 212, 170, 0.2);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  text-align: center;
  backdrop-filter: blur(10px);
}

.proof-statement {
  color: white;
  font-size: var(--font-size-lg);
  margin-bottom: 0;
  font-weight: var(--font-weight-medium);
}

/* Professional Development */
.professional-development {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.featured-card {
  background: var(--background-card);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-medium);
  border: 2px solid var(--primary-color);
  position: relative;
  overflow: hidden;
}

.featured-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.card-header i {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
  padding: var(--spacing-md);
  background: rgba(0, 212, 170, 0.1);
  border-radius: var(--border-radius-md);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-header h4 {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  margin-bottom: 0;
}

.card-content p {
  margin-bottom: var(--spacing-md);
}

.card-content ul {
  list-style: none;
  margin-bottom: var(--spacing-lg);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-sm);
}

.card-content li {
  position: relative;
  padding-left: var(--spacing-lg);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.card-content li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

.blog-impact {
  background: var(--background-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  border-left: 4px solid var(--secondary-color);
}

.blog-impact p {
  margin-bottom: 0;
  font-style: italic;
}

.development-highlights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.highlight-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
}

.highlight-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
  border-color: var(--secondary-color);
}

.highlight-card i {
  font-size: var(--font-size-2xl);
  color: var(--secondary-color);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-lg);
  background: rgba(108, 92, 231, 0.1);
  border-radius: 50%;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.highlight-card h4 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.highlight-card p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: 0;
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: var(--spacing-4xl) 0;
  background: var(--background-secondary);
}

.contact-content {
  max-width: 600px;
  margin: 0 auto;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  transition: transform var(--transition-normal);
}

.contact-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.contact-item i {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
  min-width: 40px;
}

.contact-item h4 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.contact-item p,
.contact-item a {
  color: var(--text-secondary);
  margin-bottom: 0;
}

.contact-item a:hover {
  color: var(--primary-color);
}

/* ===== FOOTER ===== */
.footer {
  background: var(--background-dark);
  color: var(--text-light);
  padding: var(--spacing-2xl) 0;
  text-align: center;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.footer-links {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  transition: all var(--transition-normal);
}

.footer-links a:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
/* Tablet Styles (768px - 1024px) */
@media (max-width: 1024px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  .hero-content {
    gap: var(--spacing-2xl);
  }

  .hero-name {
    font-size: var(--font-size-4xl);
    animation:
      gradient-shift 4s ease-in-out 3s infinite,
      glow 2s ease-in-out 3s infinite,
      bounceIn 0.8s ease-out 0.5s both;
  }

  .timeline::before {
    left: 20px;
  }

  .timeline-item {
    width: 100%;
    left: 0 !important;
    padding-left: 50px !important;
    padding-right: 0 !important;
  }

  .timeline-content::after {
    display: none;
  }

  .timeline-dot {
    left: 12px !important;
    right: auto !important;
  }

  .development-highlights {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Mobile Styles (320px - 767px) */
@media (max-width: 767px) {
  :root {
    --font-size-5xl: 2.5rem;
    --font-size-4xl: 2rem;
    --font-size-3xl: 1.5rem;
    --spacing-4xl: 3rem;
  }

  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: var(--background-primary);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: var(--spacing-2xl);
    transition: left var(--transition-normal);
    box-shadow: var(--shadow-medium);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu a {
    font-size: var(--font-size-lg);
    padding: var(--spacing-lg);
  }

  .nav-toggle {
    display: flex;
  }

  .nav-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .nav-toggle.active span:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }



  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }

  .hero-text {
    order: 2;
  }

  .hero-image {
    order: 1;
  }

  .hero-image img {
    max-width: 200px;
  }

  .hero-cta {
    justify-content: center;
  }

  .btn {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-sm);
  }

  .about-highlights {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .highlight-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .contact-info {
    grid-template-columns: 1fr;
  }

  .contact-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .education-cards {
    grid-template-columns: 1fr;
  }

  .development-highlights {
    grid-template-columns: 1fr;
  }

  .card-content ul {
    grid-template-columns: 1fr;
  }

  .subsection-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .section-header {
    margin-bottom: var(--spacing-2xl);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .section-subtitle {
    font-size: var(--font-size-base);
  }
}

/* Extra small devices */
@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  .hero-name {
    font-size: var(--font-size-3xl);
    animation:
      gradient-shift 3s ease-in-out 3s infinite,
      glow 2s ease-in-out 3s infinite,
      bounceIn 0.8s ease-out 0.5s both;
  }

  .hero-description {
    font-size: var(--font-size-base);
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 200px;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}

/* Focus styles for better accessibility */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.4);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.5);
  }
}

/* Print styles */
@media print {
  .navbar,
  .hero-scroll,
  .footer {
    display: none;
  }

  .hero {
    min-height: auto;
    padding: var(--spacing-2xl) 0;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .hero-image img {
    max-width: 150px;
  }

  * {
    box-shadow: none !important;
  }
}